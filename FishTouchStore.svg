<svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <!-- Warehouse body (light blue outline) -->
  <rect x="45" y="80" width="110" height="80" fill="none" stroke="#3399cc" stroke-width="4" rx="4"/>

  <!-- Warehouse roof (darker blue outline) -->
  <path d="M40 80 L100 40 L160 80" fill="none" stroke="#006699" stroke-width="4"/>

  <!-- Warehouse door (blue-green outline) -->
  <rect x="80" y="120" width="40" height="40" fill="none" stroke="#33cccc" stroke-width="3"/>

  <!-- Centered fish symbol with ocean gradient -->
  <defs>
    <linearGradient id="fishGradient" x1="0" y1="0" x2="1" y2="0">
      <stop offset="0%" stop-color="#66ccff"/>
      <stop offset="100%" stop-color="#3399cc"/>
    </linearGradient>
  </defs>
  <g transform="translate(100,100)">
    <!-- <PERSON> body -->
    <ellipse cx="0" cy="0" rx="18" ry="9" fill="url(#fishGradient)" stroke="#006699" stroke-width="3"/>
    <!-- Fish tail -->
    <path d="M18 0 L28 -7 M18 0 L28 7" stroke="#006699" stroke-width="3" fill="none"/>
    <!-- Fish eye -->
    <circle cx="-8" cy="0" r="2" fill="#003344"/>
  </g>
</svg>
