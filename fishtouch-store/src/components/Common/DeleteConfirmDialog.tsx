import React from "react";
import {
  Dialog,
  Button,
  Portal,
  CloseButton,
  Text,
  Stack,
} from "@chakra-ui/react";

interface DeleteConfirmDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  title: string;
  description: string;
  loading?: boolean;
}

const DeleteConfirmDialog: React.FC<DeleteConfirmDialogProps> = ({
  open,
  onOpenChange,
  onConfirm,
  title,
  description,
  loading = false,
}) => {
  const handleOpenChange = (details: any) => {
    onOpenChange(details.open);
  };

  const handleClose = () => {
    onOpenChange(false);
  };

  return (
    <Dialog.Root open={open} onOpenChange={handleOpenChange}>
      <Portal>
        <Dialog.Backdrop />
        <Dialog.Positioner>
          <Dialog.Content>
            <Dialog.Header>
              <Dialog.Title fontSize="lg" fontWeight="bold">
                {title}
              </Dialog.Title>
              <Dialog.CloseTrigger asChild>
                <CloseButton size="sm" />
              </Dialog.CloseTrigger>
            </Dialog.Header>

            <Dialog.Body>
              <Text>{description}</Text>
            </Dialog.Body>

            <Dialog.Footer>
              <Stack direction="row" gap="3">
                <Button variant="outline" onClick={handleClose}>
                  取消
                </Button>
                <Button
                  colorPalette="red"
                  onClick={onConfirm}
                  loading={loading}
                  loadingText="删除中..."
                >
                  删除
                </Button>
              </Stack>
            </Dialog.Footer>
          </Dialog.Content>
        </Dialog.Positioner>
      </Portal>
    </Dialog.Root>
  );
};

export default DeleteConfirmDialog;
