
import {
  IconButton,
  Flex,
  Stack,
  Text,
  Menu,
  MenuItem,
  Avatar,
  Box,
  FlexProps,
} from "@chakra-ui/react";
import { Menu as MenuI<PERSON>, <PERSON>, Setting<PERSON> } from "lucide-react";

interface HeaderProps extends FlexProps {
  onOpen?: () => void;
}

const Header = ({ onOpen, ...rest }: HeaderProps) => {
  return (
    <Flex
      ml={{ base: 0, md: 60 }}
      px={{ base: 4, md: 4 }}
      height="20"
      alignItems="center"
      bg="white"
      borderBottomWidth="1px"
      borderBottomColor="gray.200"
      justifyContent={{ base: "space-between", md: "flex-end" }}
      {...rest}
    >
      <IconButton
        display={{ base: "flex", md: "none" }}
        onClick={onOpen}
        variant="outline"
        aria-label="open menu"
      >
        <MenuIcon />
      </IconButton>

      <Text
        display={{ base: "flex", md: "none" }}
        fontSize="2xl"
        fontFamily="monospace"
        fontWeight="bold"
      >
        FishTouch Store
      </Text>

      <Stack direction="row" gap={{ base: "0", md: "6" }}>
        <IconButton
          size="lg"
          variant="ghost"
          aria-label="notifications"
        >
          <Bell />
        </IconButton>
        <Flex alignItems={"center"}>
          <Menu.Root>
            <Menu.Trigger asChild>
              <Box
                py={2}
                transition="all 0.3s"
                cursor="pointer"
              >
                <Stack direction="row" align="center">
                  <Avatar.Root size={"sm"}>
                    <Avatar.Image
                      src="https://images.unsplash.com/photo-1619946794135-5bc917a27793?ixlib=rb-0.3.5&q=80&fm=jpg&crop=faces&fit=crop&h=200&w=200&s=b616b2c5b373a80ffc9636ba24f7a4a9"
                    />
                  </Avatar.Root>
                  <Stack
                    direction="column"
                    display={{ base: "none", md: "flex" }}
                    align="flex-start"
                    gap="1px"
                    ml="2"
                  >
                    <Text fontSize="sm">管理员</Text>
                    <Text fontSize="xs" color="gray.600">
                      Admin
                    </Text>
                  </Stack>
                  <Box display={{ base: "none", md: "flex" }}>
                    <Settings />
                  </Box>
                </Stack>
              </Box>
            </Menu.Trigger>
            <Menu.Content>
              <MenuItem value="profile">个人资料</MenuItem>
              <MenuItem value="settings">设置</MenuItem>
              <MenuItem value="logout">退出登录</MenuItem>
            </Menu.Content>
          </Menu.Root>
        </Flex>
      </Stack>
    </Flex>
  );
};

export default Header;
