import React from "react";
import {
  Box,
  CloseButton,
  Flex,
  Icon,
  Text,
  Drawer,
  useDisclosure,
  BoxProps,
  FlexProps,
} from "@chakra-ui/react";
import {
  Package,
  ShoppingCart,
  Truck,
  BarChart3,
  AlertTriangle,
  Home,
  Archive,
  FileText,
} from "lucide-react";
import { Link, useLocation } from "react-router-dom";

interface LinkItemProps {
  name: string;
  icon: any;
  path: string;
}

const LinkItems: Array<LinkItemProps> = [
  { name: "仪表板", icon: Home, path: "/" },
  { name: "包装管理", icon: Package, path: "/packages" },
  { name: "单品管理", icon: Archive, path: "/items" },
  { name: "配方管理", icon: FileText, path: "/recipes" },
  { name: "批次管理", icon: BarChart3, path: "/batches" },
  { name: "采购订单", icon: ShoppingCart, path: "/purchase-orders" },
  { name: "发货订单", icon: Truck, path: "/shipment-orders" },
  { name: "库存预警", icon: Alert<PERSON>riangle, path: "/alerts" },
];

export default function Sidebar() {
  const { open, onClose } = useDisclosure();
  return (
    <Box>
      <SidebarContent
        onClose={() => onClose}
        display={{ base: "none", md: "block" }}
      />
      <Drawer.Root
        open={open}
        placement="start"
        onOpenChange={(details) => !details.open && onClose()}
        size="full"
      >
        <Drawer.Content>
          <SidebarContent onClose={onClose} />
        </Drawer.Content>
      </Drawer.Root>
    </Box>
  );
}

interface SidebarProps extends BoxProps {
  onClose: () => void;
}

const SidebarContent = ({ onClose, ...rest }: SidebarProps) => {
  return (
    <Box
      bg="white"
      borderRight="1px"
      borderRightColor="gray.200"
      w={{ base: "full", md: 60 }}
      pos="fixed"
      h="full"
      {...rest}
    >
      <Flex h="20" alignItems="center" mx="8" justifyContent="space-between">
        <Text fontSize="2xl" fontFamily="monospace" fontWeight="bold">
          FishTouch Store
        </Text>
        <CloseButton display={{ base: "flex", md: "none" }} onClick={onClose} />
      </Flex>
      {LinkItems.map((link) => (
        <NavItem key={link.name} icon={link.icon} path={link.path}>
          {link.name}
        </NavItem>
      ))}
    </Box>
  );
};

interface NavItemProps extends FlexProps {
  icon: any;
  path: string;
  children: React.ReactNode;
}

const NavItem = ({ icon, path, children, ...rest }: NavItemProps) => {
  const location = useLocation();
  const isActive = location.pathname === path;

  return (
    <Link to={path} style={{ textDecoration: "none" }}>
      <Flex
        align="center"
        p="4"
        mx="4"
        borderRadius="lg"
        role="group"
        cursor="pointer"
        bg={isActive ? "blue.400" : "transparent"}
        color={isActive ? "white" : "inherit"}
        _hover={{
          bg: isActive ? "blue.400" : "blue.50",
          color: isActive ? "white" : "blue.600",
        }}
        {...rest}
      >
        {icon && (
          <Icon
            mr="4"
            fontSize="16"
            as={icon}
          />
        )}
        {children}
      </Flex>
    </Link>
  );
};
