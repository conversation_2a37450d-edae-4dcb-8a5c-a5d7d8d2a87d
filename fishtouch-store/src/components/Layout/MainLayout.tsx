import React from "react";
import {
  Box,
  Flex,
  useColorModeValue,
} from "@chakra-ui/react";
import Sidebar from "./Sidebar";
import Header from "./Header";

interface MainLayoutProps {
  children: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const bg = useColorModeValue("gray.50", "gray.900");

  return (
    <Box minH="100vh" bg={bg}>
      <Sidebar />
      <Box ml={{ base: 0, md: 60 }}>
        <Header />
        <Box p={4}>
          {children}
        </Box>
      </Box>
    </Box>
  );
};

export default MainLayout;
