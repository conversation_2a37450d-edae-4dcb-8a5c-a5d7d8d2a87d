import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON>dal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  VStack,
  useToast,
  FormErrorMessage,
} from "@chakra-ui/react";
import { packageApi } from "../../services/api";
import type { Package, NewPackage } from "../../types";

interface PackageModalProps {
  isOpen: boolean;
  onClose: () => void;
  package?: Package | null;
  onSuccess: () => void;
}

const PackageModal: React.FC<PackageModalProps> = ({
  isOpen,
  onClose,
  package: editPackage,
  onSuccess,
}) => {
  const [formData, setFormData] = useState<NewPackage>({
    name: "",
    description: "",
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  const toast = useToast();
  const isEdit = !!editPackage;

  useEffect(() => {
    if (isOpen) {
      if (editPackage) {
        setFormData({
          name: editPackage.name,
          description: editPackage.description || "",
        });
      } else {
        setFormData({
          name: "",
          description: "",
        });
      }
      setErrors({});
    }
  }, [isOpen, editPackage]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = "包装名称不能为空";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);
      
      if (isEdit) {
        // 注意：当前后端API没有update方法，这里需要后端支持
        // 暂时显示提示信息
        toast({
          title: "提示",
          description: "编辑功能需要后端API支持",
          status: "info",
          duration: 3000,
          isClosable: true,
        });
      } else {
        await packageApi.create(formData);
        toast({
          title: "成功",
          description: "包装创建成功",
          status: "success",
          duration: 3000,
          isClosable: true,
        });
      }
      
      onSuccess();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "操作失败";
      toast({
        title: "错误",
        description: errorMessage,
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof NewPackage, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
    
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: "",
      }));
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="md">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>{isEdit ? "编辑包装" : "新增包装"}</ModalHeader>
        <ModalCloseButton />
        
        <ModalBody>
          <VStack spacing={4}>
            <FormControl isRequired isInvalid={!!errors.name}>
              <FormLabel>包装名称</FormLabel>
              <Input
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                placeholder="请输入包装名称"
              />
              <FormErrorMessage>{errors.name}</FormErrorMessage>
            </FormControl>

            <FormControl>
              <FormLabel>描述</FormLabel>
              <Textarea
                value={formData.description}
                onChange={(e) => handleInputChange("description", e.target.value)}
                placeholder="请输入包装描述（可选）"
                rows={3}
              />
            </FormControl>
          </VStack>
        </ModalBody>

        <ModalFooter>
          <Button variant="ghost" mr={3} onClick={onClose}>
            取消
          </Button>
          <Button
            colorScheme="blue"
            onClick={handleSubmit}
            isLoading={loading}
            loadingText={isEdit ? "保存中..." : "创建中..."}
          >
            {isEdit ? "保存" : "创建"}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default PackageModal;
