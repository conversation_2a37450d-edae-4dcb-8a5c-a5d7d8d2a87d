import React, { useState, useEffect } from "react";
import {
  <PERSON>alog,
  Button,
  Input,
  Textarea,
  Stack,
  Text,
  Portal,
  CloseButton,
} from "@chakra-ui/react";
import { packageApi } from "../../services/api";
import type { Package, NewPackage } from "../../types";

interface PackageModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  package?: Package | null;
  onSuccess: () => void;
}

const PackageModal: React.FC<PackageModalProps> = ({
  open,
  onOpenChange,
  package: editPackage,
  onSuccess,
}) => {
  const [formData, setFormData] = useState<NewPackage>({
    name: "",
    description: "",
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  const isEdit = !!editPackage;

  useEffect(() => {
    if (open) {
      if (editPackage) {
        setFormData({
          name: editPackage.name,
          description: editPackage.description || "",
        });
      } else {
        setFormData({
          name: "",
          description: "",
        });
      }
      setErrors({});
    }
  }, [open, editPackage]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.name.trim()) {
      newErrors.name = "包装名称不能为空";
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;
    
    setLoading(true);
    try {
      if (isEdit && editPackage) {
        await packageApi.update(editPackage.id, formData);
      } else {
        await packageApi.create(formData);
      }
      
      onSuccess();
      onOpenChange(false);
    } catch (error) {
      console.error("保存包装失败:", error);
      alert("保存失败，请重试");
    } finally {
      setLoading(false);
    }
  };

  const handleOpenChange = (details: any) => {
    onOpenChange(details.open);
  };

  return (
    <Dialog.Root open={open} onOpenChange={handleOpenChange}>
      <Portal>
        <Dialog.Backdrop />
        <Dialog.Positioner>
          <Dialog.Content>
            <Dialog.Header>
              <Dialog.Title>{isEdit ? "编辑包装" : "新增包装"}</Dialog.Title>
              <Dialog.CloseTrigger asChild>
                <CloseButton size="sm" />
              </Dialog.CloseTrigger>
            </Dialog.Header>
            
            <Dialog.Body>
              <Stack gap="4">
                <Stack gap="2">
                  <Text>包装名称 *</Text>
                  <Input
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder="请输入包装名称"
                  />
                  {errors.name && (
                    <Text color="red.500" fontSize="sm">{errors.name}</Text>
                  )}
                </Stack>
                
                <Stack gap="2">
                  <Text>描述</Text>
                  <Textarea
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    placeholder="请输入包装描述"
                    rows={3}
                  />
                </Stack>
              </Stack>
            </Dialog.Body>
            
            <Dialog.Footer>
              <Dialog.ActionTrigger asChild>
                <Button variant="outline">取消</Button>
              </Dialog.ActionTrigger>
              <Button 
                colorPalette="blue" 
                onClick={handleSubmit}
                loading={loading}
                loadingText={isEdit ? "保存中..." : "创建中..."}
              >
                {isEdit ? "保存" : "创建"}
              </Button>
            </Dialog.Footer>
          </Dialog.Content>
        </Dialog.Positioner>
      </Portal>
    </Dialog.Root>
  );
};

export default PackageModal;
