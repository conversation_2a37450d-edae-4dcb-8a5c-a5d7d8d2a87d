import React, { useState } from "react";
import {
  Dialog,
  Button,
  Input,
  Stack,
  Text,
  Portal,
  CloseButton,
} from "@chakra-ui/react";

interface ItemModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  item?: any;
  onSuccess: () => void;
}

const ItemModal: React.FC<ItemModalProps> = ({
  open,
  onOpenChange,
  item,
  onSuccess,
}) => {
  const [name, setName] = useState(item?.name || "");
  const [sku, setSku] = useState(item?.sku || "");
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    if (!name.trim() || !sku.trim()) {
      alert("请填写完整信息");
      return;
    }
    
    setLoading(true);
    // 模拟API调用
    setTimeout(() => {
      setLoading(false);
      onSuccess();
      onOpenChange(false);
    }, 1000);
  };

  const handleOpenChange = (details: any) => {
    onOpenChange(details.open);
  };

  return (
    <Dialog.Root open={open} onOpenChange={handleOpenChange}>
      <Portal>
        <Dialog.Backdrop />
        <Dialog.Positioner>
          <Dialog.Content>
            <Dialog.Header>
              <Dialog.Title>{item ? "编辑单品" : "新增单品"}</Dialog.Title>
              <Dialog.CloseTrigger asChild>
                <CloseButton size="sm" />
              </Dialog.CloseTrigger>
            </Dialog.Header>
            
            <Dialog.Body>
              <Stack gap="4">
                <Stack gap="2">
                  <Text>单品名称</Text>
                  <Input
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    placeholder="请输入单品名称"
                  />
                </Stack>
                
                <Stack gap="2">
                  <Text>SKU</Text>
                  <Input
                    value={sku}
                    onChange={(e) => setSku(e.target.value)}
                    placeholder="请输入SKU"
                  />
                </Stack>
              </Stack>
            </Dialog.Body>
            
            <Dialog.Footer>
              <Dialog.ActionTrigger asChild>
                <Button variant="outline">取消</Button>
              </Dialog.ActionTrigger>
              <Button 
                colorPalette="blue" 
                onClick={handleSubmit}
                loading={loading}
              >
                {item ? "保存" : "创建"}
              </Button>
            </Dialog.Footer>
          </Dialog.Content>
        </Dialog.Positioner>
      </Portal>
    </Dialog.Root>
  );
};

export default ItemModal;
