import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON>lay,
  <PERSON>dal<PERSON>ontent,
  <PERSON>dal<PERSON>eader,
  <PERSON>dal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Input,
  VStack,
  useToast,
  FormErrorMessage,
  HStack,
  Text,
} from "@chakra-ui/react";
import { itemApi } from "../../services/api";
import type { Item, NewItem } from "../../types";

interface ItemModalProps {
  isOpen: boolean;
  onClose: () => void;
  item?: Item | null;
  onSuccess: () => void;
}

const ItemModal: React.FC<ItemModalProps> = ({
  isOpen,
  onClose,
  item: editItem,
  onSuccess,
}) => {
  const [formData, setFormData] = useState<NewItem>({
    name: "",
    sku: "",
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  const toast = useToast();
  const isEdit = !!editItem;

  useEffect(() => {
    if (isOpen) {
      if (editItem) {
        setFormData({
          name: editItem.name,
          sku: editItem.sku,
        });
      } else {
        setFormData({
          name: "",
          sku: "",
        });
      }
      setErrors({});
    }
  }, [isOpen, editItem]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = "单品名称不能为空";
    }

    if (!formData.sku.trim()) {
      newErrors.sku = "SKU不能为空";
    } else if (!/^[A-Z0-9-_]+$/i.test(formData.sku)) {
      newErrors.sku = "SKU只能包含字母、数字、连字符和下划线";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const generateSKU = () => {
    if (!formData.name.trim()) {
      toast({
        title: "提示",
        description: "请先输入单品名称",
        status: "info",
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    // 简单的SKU生成逻辑：取名称的拼音首字母 + 时间戳后4位
    const timestamp = Date.now().toString().slice(-4);
    const namePrefix = formData.name
      .replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, "")
      .slice(0, 4)
      .toUpperCase();
    
    const generatedSKU = `${namePrefix}${timestamp}`;
    setFormData(prev => ({
      ...prev,
      sku: generatedSKU,
    }));
    
    // 清除SKU字段的错误
    if (errors.sku) {
      setErrors(prev => ({
        ...prev,
        sku: "",
      }));
    }
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);
      
      if (isEdit) {
        // 注意：当前后端API没有update方法，这里需要后端支持
        toast({
          title: "提示",
          description: "编辑功能需要后端API支持",
          status: "info",
          duration: 3000,
          isClosable: true,
        });
      } else {
        await itemApi.create(formData);
        toast({
          title: "成功",
          description: "单品创建成功",
          status: "success",
          duration: 3000,
          isClosable: true,
        });
      }
      
      onSuccess();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "操作失败";
      toast({
        title: "错误",
        description: errorMessage,
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof NewItem, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
    
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: "",
      }));
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="md">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>{isEdit ? "编辑单品" : "新增单品"}</ModalHeader>
        <ModalCloseButton />
        
        <ModalBody>
          <VStack spacing={4}>
            <FormControl isRequired isInvalid={!!errors.name}>
              <FormLabel>单品名称</FormLabel>
              <Input
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                placeholder="请输入单品名称"
              />
              <FormErrorMessage>{errors.name}</FormErrorMessage>
            </FormControl>

            <FormControl isRequired isInvalid={!!errors.sku}>
              <FormLabel>SKU</FormLabel>
              <VStack spacing={2} align="stretch">
                <Input
                  value={formData.sku}
                  onChange={(e) => handleInputChange("sku", e.target.value.toUpperCase())}
                  placeholder="请输入SKU或点击生成"
                />
                <HStack>
                  <Button size="sm" variant="outline" onClick={generateSKU}>
                    自动生成SKU
                  </Button>
                  <Text fontSize="xs" color="gray.500">
                    SKU应为唯一标识符
                  </Text>
                </HStack>
              </VStack>
              <FormErrorMessage>{errors.sku}</FormErrorMessage>
            </FormControl>
          </VStack>
        </ModalBody>

        <ModalFooter>
          <Button variant="ghost" mr={3} onClick={onClose}>
            取消
          </Button>
          <Button
            colorScheme="blue"
            onClick={handleSubmit}
            isLoading={loading}
            loadingText={isEdit ? "保存中..." : "创建中..."}
          >
            {isEdit ? "保存" : "创建"}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default ItemModal;
