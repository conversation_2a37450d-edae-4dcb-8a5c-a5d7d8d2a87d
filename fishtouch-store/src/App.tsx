
import { Routes, Route } from "react-router-dom";
import MainLayout from "./components/Layout/MainLayout";
import Dashboard from "./pages/Dashboard";
import PackageManagement from "./pages/PackageManagement";
import ItemManagement from "./pages/ItemManagement";

function App() {
  return (
    <MainLayout>
      <Routes>
        <Route path="/" element={<Dashboard />} />
        <Route path="/packages" element={<PackageManagement />} />
        <Route path="/items" element={<ItemManagement />} />
        <Route path="/recipes" element={<div>配方管理页面开发中...</div>} />
        <Route path="/batches" element={<div>批次管理页面开发中...</div>} />
        <Route path="/purchase-orders" element={<div>采购订单页面开发中...</div>} />
        <Route path="/shipment-orders" element={<div>发货订单页面开发中...</div>} />
        <Route path="/alerts" element={<div>库存预警页面开发中...</div>} />
      </Routes>
    </MainLayout>
  );
}

export default App;
