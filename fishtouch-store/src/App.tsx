
import React from "react";
import { Box, Heading, Text, Stack, Button } from "@chakra-ui/react";
import { Routes, Route, Link, useLocation } from "react-router-dom";

function Dashboard() {
  return (
    <Box p={8}>
      <Heading size="lg" mb={4}>
        仪表板
      </Heading>
      <Text>
        欢迎使用 FishTouch Store 库存管理系统！
      </Text>
    </Box>
  );
}

interface Package {
  id: number;
  name: string;
  description: string;
}

function PackagePage() {
  const [packages, setPackages] = React.useState<Package[]>([]);
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    // 模拟API调用
    setTimeout(() => {
      setPackages([
        { id: 1, name: "小包装", description: "适合小批量商品" },
        { id: 2, name: "中包装", description: "适合中等批量商品" },
        { id: 3, name: "大包装", description: "适合大批量商品" },
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  if (loading) {
    return (
      <Box p={8}>
        <Heading size="lg" mb={4}>
          包装管理
        </Heading>
        <Text>加载中...</Text>
      </Box>
    );
  }

  return (
    <Box p={8}>
      <Heading size="lg" mb={4}>
        包装管理
      </Heading>
      <Stack direction="column" gap="4">
        {packages.map((pkg) => (
          <Box key={pkg.id} p="4" border="1px" borderColor="gray.200" borderRadius="md">
            <Heading size="md">{pkg.name}</Heading>
            <Text color="gray.600">{pkg.description}</Text>
          </Box>
        ))}
      </Stack>
    </Box>
  );
}

function ItemPage() {
  return (
    <Box p={8}>
      <Heading size="lg" mb={4}>
        单品管理
      </Heading>
      <Text>
        单品管理页面
      </Text>
    </Box>
  );
}

function App() {
  const location = useLocation();

  return (
    <Box display="flex" minH="100vh">
      {/* 简单的侧边栏 */}
      <Box w="200px" bg="gray.100" p={4}>
        <Stack direction="column" gap="2">
          <Button asChild w="full" variant={location.pathname === "/" ? "solid" : "ghost"}>
            <Link to="/">仪表板</Link>
          </Button>
          <Button asChild w="full" variant={location.pathname === "/packages" ? "solid" : "ghost"}>
            <Link to="/packages">包装管理</Link>
          </Button>
          <Button asChild w="full" variant={location.pathname === "/items" ? "solid" : "ghost"}>
            <Link to="/items">单品管理</Link>
          </Button>
        </Stack>
      </Box>

      {/* 主内容区域 */}
      <Box flex="1">
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/packages" element={<PackagePage />} />
          <Route path="/items" element={<ItemPage />} />
        </Routes>
      </Box>
    </Box>
  );
}

export default App;
