import React, { useEffect, useState } from "react";
import {
  Box,
  SimpleGrid,
  Stat,
  Heading,
  Text,
  Alert,
  Stack,
  Badge,
  Button,
} from "@chakra-ui/react";
import { inventoryApi } from "../services/api";
import type { StockLevel, LowStockAlert } from "../types";
import { toaster } from "../components/ui/toaster";

const Dashboard: React.FC = () => {
  const [stockLevels, setStockLevels] = useState<StockLevel[]>([]);
  const [lowStockAlerts, setLowStockAlerts] = useState<LowStockAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const [stockData, alertsData] = await Promise.all([
          inventoryApi.getAllStockLevels(),
          inventoryApi.checkLowStockAlerts(),
        ]);
        setStockLevels(stockData);
        setLowStockAlerts(alertsData);
      } catch (err) {
        setError(err instanceof Error ? err.message : "获取数据失败");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const totalItems = stockLevels.length;
  const totalStock = stockLevels.reduce((sum, item) => sum + item.total_quantity, 0);
  const lowStockCount = lowStockAlerts.length;
  const expiringSoonCount = stockLevels.reduce((count, item) => {
    const expiringSoon = item.batches.filter(batch => {
      if (!batch.expiry_date) return false;
      const expiryDate = new Date(batch.expiry_date);
      const thirtyDaysFromNow = new Date();
      thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
      return expiryDate <= thirtyDaysFromNow;
    });
    return count + expiringSoon.length;
  }, 0);

  if (loading) {
    return (
      <Box>
        <Heading size="lg" mb={6}>
          仪表板
        </Heading>
        <Text>加载中...</Text>
      </Box>
    );
  }

  if (error) {
    return (
      <Box>
        <Heading size="lg" mb={6}>
          仪表板
        </Heading>
        <Alert.Root status="error">
          <Alert.Indicator />
          <Alert.Content>
            <Alert.Title>错误!</Alert.Title>
            <Alert.Description>{error}</Alert.Description>
          </Alert.Content>
        </Alert.Root>
      </Box>
    );
  }

  const testToast = () => {
    toaster.create({
      title: "测试通知",
      description: "Toast功能正常工作！",
      type: "success",
      duration: 3000,
      closable: true,
    });
  };

  return (
    <Box>
      <Stack direction="row" justify="space-between" align="center" mb={6}>
        <Heading size="lg">
          仪表板
        </Heading>
        <Button onClick={testToast} colorPalette="blue">
          测试Toast
        </Button>
      </Stack>

      {/* 统计卡片 */}
      <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} gap={6} mb={8}>
        <Box
          p={5}
          shadow="md"
          borderWidth="1px"
          borderRadius="lg"
          bg="white"
          borderColor="gray.200"
        >
          <Stat.Root>
            <Stat.Label>总单品数</Stat.Label>
            <Stat.ValueText>{totalItems}</Stat.ValueText>
            <Stat.HelpText>
              库存管理中的单品种类
            </Stat.HelpText>
          </Stat.Root>
        </Box>

        <Box
          p={5}
          shadow="md"
          borderWidth="1px"
          borderRadius="lg"
          bg="white"
          borderColor="gray.200"
        >
          <Stat.Root>
            <Stat.Label>总库存量</Stat.Label>
            <Stat.ValueText>{totalStock}</Stat.ValueText>
            <Stat.HelpText>
              所有单品的总库存数量
            </Stat.HelpText>
          </Stat.Root>
        </Box>

        <Box
          p={5}
          shadow="md"
          borderWidth="1px"
          borderRadius="lg"
          bg="white"
          borderColor="gray.200"
        >
          <Stat.Root>
            <Stat.Label>低库存预警</Stat.Label>
            <Stat.ValueText color={lowStockCount > 0 ? "red.500" : "green.500"}>
              {lowStockCount}
            </Stat.ValueText>
            <Stat.HelpText>
              需要补货的单品数量
            </Stat.HelpText>
          </Stat.Root>
        </Box>

        <Box
          p={5}
          shadow="md"
          borderWidth="1px"
          borderRadius="lg"
          bg="white"
          borderColor="gray.200"
        >
          <Stat.Root>
            <Stat.Label>即将过期</Stat.Label>
            <Stat.ValueText color={expiringSoonCount > 0 ? "orange.500" : "green.500"}>
              {expiringSoonCount}
            </Stat.ValueText>
            <Stat.HelpText>
              30天内过期的批次数量
            </Stat.HelpText>
          </Stat.Root>
        </Box>
      </SimpleGrid>

      {/* 低库存预警 */}
      {lowStockAlerts.length > 0 && (
        <Box mb={8}>
          <Heading size="md" mb={4}>
            低库存预警
          </Heading>
          <Stack direction="column" gap={3} align="stretch">
            {lowStockAlerts.map(([item, alert, currentStock]) => (
              <Alert.Root status="warning" key={item.id}>
                <Alert.Indicator />
                <Alert.Content>
                  <Alert.Title>
                    {item.name} (SKU: {item.sku})
                  </Alert.Title>
                  <Alert.Description>
                    当前库存: {currentStock} | 预警阈值: {alert.threshold}
                  </Alert.Description>
                </Alert.Content>
                <Badge colorPalette="orange">需要补货</Badge>
              </Alert.Root>
            ))}
          </Stack>
        </Box>
      )}

      {/* 库存概览 */}
      <Box>
        <Heading size="md" mb={4}>
          库存概览
        </Heading>
        <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} gap={4}>
          {stockLevels.slice(0, 6).map((stock) => (
            <Box
              key={stock.item_id}
              p={4}
              shadow="sm"
              borderWidth="1px"
              borderRadius="md"
              bg="white"
              borderColor="gray.200"
            >
              <Stack direction="column" align="start" gap={2}>
                <Stack direction="row" justify="space-between" w="full">
                  <Text fontWeight="bold">{stock.item_name}</Text>
                  <Badge colorPalette={stock.total_quantity > 0 ? "green" : "red"}>
                    {stock.total_quantity}
                  </Badge>
                </Stack>
                <Text fontSize="sm" color="gray.600">
                  SKU: {stock.item_sku}
                </Text>
                <Text fontSize="sm">
                  批次数量: {stock.batches.length}
                </Text>
              </Stack>
            </Box>
          ))}
        </SimpleGrid>
      </Box>
    </Box>
  );
};

export default Dashboard;
