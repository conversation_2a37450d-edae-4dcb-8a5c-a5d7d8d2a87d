import React, { useEffect, useState } from "react";
import {
  Box,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  useColorModeValue,
  Heading,
  Text,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  VStack,
  HStack,
  Badge,
} from "@chakra-ui/react";
import { inventoryApi } from "../services/api";
import type { StockLevel, LowStockAlert } from "../types";

const Dashboard: React.FC = () => {
  const [stockLevels, setStockLevels] = useState<StockLevel[]>([]);
  const [lowStockAlerts, setLowStockAlerts] = useState<LowStockAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const cardBg = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.200", "gray.700");

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const [stockData, alertsData] = await Promise.all([
          inventoryApi.getAllStockLevels(),
          inventoryApi.checkLowStockAlerts(),
        ]);
        setStockLevels(stockData);
        setLowStockAlerts(alertsData);
      } catch (err) {
        setError(err instanceof Error ? err.message : "获取数据失败");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const totalItems = stockLevels.length;
  const totalStock = stockLevels.reduce((sum, item) => sum + item.total_quantity, 0);
  const lowStockCount = lowStockAlerts.length;
  const expiringSoonCount = stockLevels.reduce((count, item) => {
    const expiringSoon = item.batches.filter(batch => {
      if (!batch.expiry_date) return false;
      const expiryDate = new Date(batch.expiry_date);
      const thirtyDaysFromNow = new Date();
      thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
      return expiryDate <= thirtyDaysFromNow;
    });
    return count + expiringSoon.length;
  }, 0);

  if (loading) {
    return (
      <Box>
        <Heading size="lg" mb={6}>
          仪表板
        </Heading>
        <Text>加载中...</Text>
      </Box>
    );
  }

  if (error) {
    return (
      <Box>
        <Heading size="lg" mb={6}>
          仪表板
        </Heading>
        <Alert status="error">
          <AlertIcon />
          <AlertTitle>错误!</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </Box>
    );
  }

  return (
    <Box>
      <Heading size="lg" mb={6}>
        仪表板
      </Heading>

      {/* 统计卡片 */}
      <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6} mb={8}>
        <Box
          p={5}
          shadow="md"
          borderWidth="1px"
          borderRadius="lg"
          bg={cardBg}
          borderColor={borderColor}
        >
          <Stat>
            <StatLabel>总单品数</StatLabel>
            <StatNumber>{totalItems}</StatNumber>
            <StatHelpText>
              <StatArrow type="increase" />
              库存管理中的单品种类
            </StatHelpText>
          </Stat>
        </Box>

        <Box
          p={5}
          shadow="md"
          borderWidth="1px"
          borderRadius="lg"
          bg={cardBg}
          borderColor={borderColor}
        >
          <Stat>
            <StatLabel>总库存量</StatLabel>
            <StatNumber>{totalStock}</StatNumber>
            <StatHelpText>
              <StatArrow type="increase" />
              所有单品的总库存数量
            </StatHelpText>
          </Stat>
        </Box>

        <Box
          p={5}
          shadow="md"
          borderWidth="1px"
          borderRadius="lg"
          bg={cardBg}
          borderColor={borderColor}
        >
          <Stat>
            <StatLabel>低库存预警</StatLabel>
            <StatNumber color={lowStockCount > 0 ? "red.500" : "green.500"}>
              {lowStockCount}
            </StatNumber>
            <StatHelpText>
              需要补货的单品数量
            </StatHelpText>
          </Stat>
        </Box>

        <Box
          p={5}
          shadow="md"
          borderWidth="1px"
          borderRadius="lg"
          bg={cardBg}
          borderColor={borderColor}
        >
          <Stat>
            <StatLabel>即将过期</StatLabel>
            <StatNumber color={expiringSoonCount > 0 ? "orange.500" : "green.500"}>
              {expiringSoonCount}
            </StatNumber>
            <StatHelpText>
              30天内过期的批次数量
            </StatHelpText>
          </Stat>
        </Box>
      </SimpleGrid>

      {/* 低库存预警 */}
      {lowStockAlerts.length > 0 && (
        <Box mb={8}>
          <Heading size="md" mb={4}>
            低库存预警
          </Heading>
          <VStack spacing={3} align="stretch">
            {lowStockAlerts.map(([item, alert, currentStock]) => (
              <Alert status="warning" key={item.id}>
                <AlertIcon />
                <Box flex="1">
                  <AlertTitle>
                    {item.name} (SKU: {item.sku})
                  </AlertTitle>
                  <AlertDescription>
                    当前库存: {currentStock} | 预警阈值: {alert.threshold}
                  </AlertDescription>
                </Box>
                <Badge colorScheme="orange">需要补货</Badge>
              </Alert>
            ))}
          </VStack>
        </Box>
      )}

      {/* 库存概览 */}
      <Box>
        <Heading size="md" mb={4}>
          库存概览
        </Heading>
        <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={4}>
          {stockLevels.slice(0, 6).map((stock) => (
            <Box
              key={stock.item_id}
              p={4}
              shadow="sm"
              borderWidth="1px"
              borderRadius="md"
              bg={cardBg}
              borderColor={borderColor}
            >
              <VStack align="start" spacing={2}>
                <HStack justify="space-between" w="full">
                  <Text fontWeight="bold">{stock.item_name}</Text>
                  <Badge colorScheme={stock.total_quantity > 0 ? "green" : "red"}>
                    {stock.total_quantity}
                  </Badge>
                </HStack>
                <Text fontSize="sm" color="gray.600">
                  SKU: {stock.item_sku}
                </Text>
                <Text fontSize="sm">
                  批次数量: {stock.batches.length}
                </Text>
              </VStack>
            </Box>
          ))}
        </SimpleGrid>
      </Box>
    </Box>
  );
};

export default Dashboard;
