import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>ing,
  <PERSON>,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
  useDisclosure,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  HStack,
  IconButton,
  Text,
  useToast,
  Spinner,
  Center,
  Badge,
  Input,
  InputGroup,
  InputLeftElement,
} from "@chakra-ui/react";
import { Plus, Edit, Trash2, Search } from "lucide-react";
import { itemApi } from "../services/api";
import type { Item } from "../types";
import ItemModal from "../components/Item/ItemModal";
import DeleteConfirmDialog from "../components/Common/DeleteConfirmDialog";

const ItemManagement: React.FC = () => {
  const [items, setItems] = useState<Item[]>([]);
  const [filteredItems, setFilteredItems] = useState<Item[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedItem, setSelectedItem] = useState<Item | null>(null);
  const [deleteItem, setDeleteItem] = useState<Item | null>(null);
  const [searchTerm, setSearchTerm] = useState("");

  const { isOpen: isModalOpen, onOpen: onModalOpen, onClose: onModalClose } = useDisclosure();
  const { isOpen: isDeleteOpen, onOpen: onDeleteOpen, onClose: onDeleteClose } = useDisclosure();

  const toast = useToast();

  const fetchItems = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await itemApi.getAll();
      setItems(data);
      setFilteredItems(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "获取单品列表失败";
      setError(errorMessage);
      toast({
        title: "错误",
        description: errorMessage,
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchItems();
  }, []);

  // 搜索过滤
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredItems(items);
    } else {
      const filtered = items.filter(item =>
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.sku.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredItems(filtered);
    }
  }, [searchTerm, items]);

  const handleCreate = () => {
    setSelectedItem(null);
    onModalOpen();
  };

  const handleEdit = (item: Item) => {
    setSelectedItem(item);
    onModalOpen();
  };

  const handleDeleteClick = (item: Item) => {
    setDeleteItem(item);
    onDeleteOpen();
  };

  const handleDeleteConfirm = async () => {
    if (!deleteItem) return;

    try {
      await itemApi.delete(deleteItem.id);
      toast({
        title: "成功",
        description: "单品删除成功",
        status: "success",
        duration: 3000,
        isClosable: true,
      });
      fetchItems();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "删除单品失败";
      toast({
        title: "错误",
        description: errorMessage,
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setDeleteItem(null);
      onDeleteClose();
    }
  };

  const handleModalSuccess = () => {
    onModalClose();
    fetchItems();
  };

  if (loading) {
    return (
      <Box>
        <Heading size="lg" mb={6}>
          单品管理
        </Heading>
        <Center>
          <Spinner size="xl" />
        </Center>
      </Box>
    );
  }

  return (
    <Box>
      <HStack justify="space-between" mb={6}>
        <Heading size="lg">单品管理</Heading>
        <Button leftIcon={<Plus />} colorScheme="blue" onClick={handleCreate}>
          新增单品
        </Button>
      </HStack>

      {error && (
        <Alert status="error" mb={6}>
          <AlertIcon />
          <AlertTitle>错误!</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* 搜索框 */}
      <Box mb={6}>
        <InputGroup maxW="400px">
          <InputLeftElement pointerEvents="none">
            <Search color="gray.300" />
          </InputLeftElement>
          <Input
            placeholder="搜索单品名称或SKU..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </InputGroup>
      </Box>

      <TableContainer>
        <Table variant="simple">
          <Thead>
            <Tr>
              <Th>ID</Th>
              <Th>单品名称</Th>
              <Th>SKU</Th>
              <Th>状态</Th>
              <Th>操作</Th>
            </Tr>
          </Thead>
          <Tbody>
            {filteredItems.length === 0 ? (
              <Tr>
                <Td colSpan={5}>
                  <Center>
                    <Text color="gray.500">
                      {searchTerm ? "未找到匹配的单品" : "暂无单品数据"}
                    </Text>
                  </Center>
                </Td>
              </Tr>
            ) : (
              filteredItems.map((item) => (
                <Tr key={item.id}>
                  <Td>{item.id}</Td>
                  <Td fontWeight="medium">{item.name}</Td>
                  <Td>
                    <Badge colorScheme="blue" variant="subtle">
                      {item.sku}
                    </Badge>
                  </Td>
                  <Td>
                    <Badge colorScheme="green">正常</Badge>
                  </Td>
                  <Td>
                    <HStack spacing={2}>
                      <IconButton
                        aria-label="编辑单品"
                        icon={<Edit />}
                        size="sm"
                        colorScheme="blue"
                        variant="ghost"
                        onClick={() => handleEdit(item)}
                      />
                      <IconButton
                        aria-label="删除单品"
                        icon={<Trash2 />}
                        size="sm"
                        colorScheme="red"
                        variant="ghost"
                        onClick={() => handleDeleteClick(item)}
                      />
                    </HStack>
                  </Td>
                </Tr>
              ))
            )}
          </Tbody>
        </Table>
      </TableContainer>

      {/* 显示搜索结果统计 */}
      {searchTerm && (
        <Box mt={4}>
          <Text fontSize="sm" color="gray.600">
            找到 {filteredItems.length} 个匹配的单品
          </Text>
        </Box>
      )}

      {/* 单品创建/编辑模态框 */}
      <ItemModal
        open={isModalOpen}
        onOpenChange={(open) => !open && onModalClose()}
        item={selectedItem}
        onSuccess={handleModalSuccess}
      />

      {/* 删除确认对话框 */}
      <DeleteConfirmDialog
        open={isDeleteOpen}
        onOpenChange={(open) => !open && onDeleteClose()}
        onConfirm={handleDeleteConfirm}
        title="删除单品"
        description={`确定要删除单品 "${deleteItem?.name}" (SKU: ${deleteItem?.sku}) 吗？此操作不可撤销。`}
      />
    </Box>
  );
};

export default ItemManagement;
