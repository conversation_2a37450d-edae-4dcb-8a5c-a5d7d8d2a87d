import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  But<PERSON>,
  Heading,
  Table,
  useDisclosure,
  Al<PERSON>,
  Stack,
  IconButton,
  Text,
  Spinner,
  Center,
} from "@chakra-ui/react";
import { Plus, Edit, Trash2 } from "lucide-react";
import { packageApi } from "../services/api";
import type { Package } from "../types";
import PackageModal from "../components/Package/PackageModal";
import DeleteConfirmDialog from "../components/Common/DeleteConfirmDialog";
import { toaster } from "../components/ui/toaster";

const PackageManagement: React.FC = () => {
  const [packages, setPackages] = useState<Package[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPackage, setSelectedPackage] = useState<Package | null>(null);
  const [deletePackage, setDeletePackage] = useState<Package | null>(null);

  const { open: isModalOpen, onOpen: onModalOpen, onClose: onModalClose } = useDisclosure();
  const { open: isDeleteOpen, onOpen: onDeleteOpen, onClose: onDeleteClose } = useDisclosure();

  // TODO: 实现 toast 功能
  // const toast = useToast();

  const fetchPackages = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await packageApi.getAll();
      setPackages(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "获取包装列表失败";
      setError(errorMessage);
      toaster.create({
        title: "错误",
        description: errorMessage,
        type: "error",
        duration: 5000,
        closable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPackages();
  }, []);

  const handleCreate = () => {
    setSelectedPackage(null);
    onModalOpen();
  };

  const handleEdit = (pkg: Package) => {
    setSelectedPackage(pkg);
    onModalOpen();
  };

  const handleDeleteClick = (pkg: Package) => {
    setDeletePackage(pkg);
    onDeleteOpen();
  };

  const handleDeleteConfirm = async () => {
    if (!deletePackage) return;

    try {
      await packageApi.delete(deletePackage.id);
      toaster.create({
        title: "成功",
        description: "包装删除成功",
        type: "success",
        duration: 3000,
        closable: true,
      });
      fetchPackages();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "删除包装失败";
      toaster.create({
        title: "错误",
        description: errorMessage,
        type: "error",
        duration: 5000,
        closable: true,
      });
    } finally {
      setDeletePackage(null);
      onDeleteClose();
    }
  };

  const handleModalSuccess = () => {
    onModalClose();
    fetchPackages();
  };

  if (loading) {
    return (
      <Box>
        <Heading size="lg" mb={6}>
          包装管理
        </Heading>
        <Center>
          <Spinner size="xl" />
        </Center>
      </Box>
    );
  }

  return (
    <Box>
      <Stack direction="row" justify="space-between" mb={6}>
        <Heading size="lg">包装管理</Heading>
        <Button colorPalette="blue" onClick={handleCreate}>
          <Plus />
          新增包装
        </Button>
      </Stack>

      {error && (
        <Alert.Root status="error" mb={6}>
          <Alert.Indicator />
          <Alert.Content>
            <Alert.Title>错误!</Alert.Title>
            <Alert.Description>{error}</Alert.Description>
          </Alert.Content>
        </Alert.Root>
      )}

      <Table.Root variant="outline">
        <Table.Header>
          <Table.Row>
            <Table.ColumnHeader>ID</Table.ColumnHeader>
            <Table.ColumnHeader>包装名称</Table.ColumnHeader>
            <Table.ColumnHeader>描述</Table.ColumnHeader>
            <Table.ColumnHeader>操作</Table.ColumnHeader>
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {packages.length === 0 ? (
            <Table.Row>
              <Table.Cell colSpan={4}>
                <Center>
                  <Text color="gray.500">暂无包装数据</Text>
                </Center>
              </Table.Cell>
            </Table.Row>
          ) : (
            packages.map((pkg) => (
              <Table.Row key={pkg.id}>
                <Table.Cell>{pkg.id}</Table.Cell>
                <Table.Cell fontWeight="medium">{pkg.name}</Table.Cell>
                <Table.Cell>{pkg.description || "-"}</Table.Cell>
                <Table.Cell>
                  <Stack direction="row" gap={2}>
                    <IconButton
                      aria-label="编辑包装"
                      size="sm"
                      colorPalette="blue"
                      variant="ghost"
                      onClick={() => handleEdit(pkg)}
                    >
                      <Edit />
                    </IconButton>
                    <IconButton
                      aria-label="删除包装"
                      size="sm"
                      colorPalette="red"
                      variant="ghost"
                      onClick={() => handleDeleteClick(pkg)}
                    >
                      <Trash2 />
                    </IconButton>
                  </Stack>
                </Table.Cell>
              </Table.Row>
            ))
          )}
        </Table.Body>
      </Table.Root>

      {/* 包装创建/编辑模态框 */}
      <PackageModal
        open={isModalOpen}
        onOpenChange={(open) => !open && onModalClose()}
        package={selectedPackage}
        onSuccess={handleModalSuccess}
      />

      {/* 删除确认对话框 */}
      <DeleteConfirmDialog
        open={isDeleteOpen}
        onOpenChange={(open) => !open && onDeleteClose()}
        onConfirm={handleDeleteConfirm}
        title="删除包装"
        description={`确定要删除包装 "${deletePackage?.name}" 吗？此操作不可撤销。`}
      />
    </Box>
  );
};

export default PackageManagement;
