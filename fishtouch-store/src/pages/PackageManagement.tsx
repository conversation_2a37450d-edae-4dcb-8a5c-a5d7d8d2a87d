import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  But<PERSON>,
  Heading,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
  useDisclosure,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  HStack,
  IconButton,
  Text,
  useToast,
  Spinner,
  Center,
} from "@chakra-ui/react";
import { Plus, Edit, Trash2 } from "lucide-react";
import { packageApi } from "../services/api";
import type { Package } from "../types";
import PackageModal from "../components/Package/PackageModal";
import DeleteConfirmDialog from "../components/Common/DeleteConfirmDialog";

const PackageManagement: React.FC = () => {
  const [packages, setPackages] = useState<Package[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPackage, setSelectedPackage] = useState<Package | null>(null);
  const [deletePackage, setDeletePackage] = useState<Package | null>(null);

  const { isOpen: isModalOpen, onOpen: onModalOpen, onClose: onModalClose } = useDisclosure();
  const { isOpen: isDeleteOpen, onOpen: onDeleteOpen, onClose: onDeleteClose } = useDisclosure();

  const toast = useToast();

  const fetchPackages = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await packageApi.getAll();
      setPackages(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "获取包装列表失败";
      setError(errorMessage);
      toast({
        title: "错误",
        description: errorMessage,
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPackages();
  }, []);

  const handleCreate = () => {
    setSelectedPackage(null);
    onModalOpen();
  };

  const handleEdit = (pkg: Package) => {
    setSelectedPackage(pkg);
    onModalOpen();
  };

  const handleDeleteClick = (pkg: Package) => {
    setDeletePackage(pkg);
    onDeleteOpen();
  };

  const handleDeleteConfirm = async () => {
    if (!deletePackage) return;

    try {
      await packageApi.delete(deletePackage.id);
      toast({
        title: "成功",
        description: "包装删除成功",
        status: "success",
        duration: 3000,
        isClosable: true,
      });
      fetchPackages();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "删除包装失败";
      toast({
        title: "错误",
        description: errorMessage,
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setDeletePackage(null);
      onDeleteClose();
    }
  };

  const handleModalSuccess = () => {
    onModalClose();
    fetchPackages();
  };

  if (loading) {
    return (
      <Box>
        <Heading size="lg" mb={6}>
          包装管理
        </Heading>
        <Center>
          <Spinner size="xl" />
        </Center>
      </Box>
    );
  }

  return (
    <Box>
      <HStack justify="space-between" mb={6}>
        <Heading size="lg">包装管理</Heading>
        <Button leftIcon={<Plus />} colorScheme="blue" onClick={handleCreate}>
          新增包装
        </Button>
      </HStack>

      {error && (
        <Alert status="error" mb={6}>
          <AlertIcon />
          <AlertTitle>错误!</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <TableContainer>
        <Table variant="simple">
          <Thead>
            <Tr>
              <Th>ID</Th>
              <Th>包装名称</Th>
              <Th>描述</Th>
              <Th>操作</Th>
            </Tr>
          </Thead>
          <Tbody>
            {packages.length === 0 ? (
              <Tr>
                <Td colSpan={4}>
                  <Center>
                    <Text color="gray.500">暂无包装数据</Text>
                  </Center>
                </Td>
              </Tr>
            ) : (
              packages.map((pkg) => (
                <Tr key={pkg.id}>
                  <Td>{pkg.id}</Td>
                  <Td fontWeight="medium">{pkg.name}</Td>
                  <Td>{pkg.description || "-"}</Td>
                  <Td>
                    <HStack spacing={2}>
                      <IconButton
                        aria-label="编辑包装"
                        icon={<Edit />}
                        size="sm"
                        colorScheme="blue"
                        variant="ghost"
                        onClick={() => handleEdit(pkg)}
                      />
                      <IconButton
                        aria-label="删除包装"
                        icon={<Trash2 />}
                        size="sm"
                        colorScheme="red"
                        variant="ghost"
                        onClick={() => handleDeleteClick(pkg)}
                      />
                    </HStack>
                  </Td>
                </Tr>
              ))
            )}
          </Tbody>
        </Table>
      </TableContainer>

      {/* 包装创建/编辑模态框 */}
      <PackageModal
        open={isModalOpen}
        onOpenChange={(open) => !open && onModalClose()}
        package={selectedPackage}
        onSuccess={handleModalSuccess}
      />

      {/* 删除确认对话框 */}
      <DeleteConfirmDialog
        open={isDeleteOpen}
        onOpenChange={(open) => !open && onDeleteClose()}
        onConfirm={handleDeleteConfirm}
        title="删除包装"
        description={`确定要删除包装 "${deletePackage?.name}" 吗？此操作不可撤销。`}
      />
    </Box>
  );
};

export default PackageManagement;
