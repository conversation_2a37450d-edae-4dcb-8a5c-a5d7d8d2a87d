// 基础数据模型类型定义
export interface Package {
  id: number;
  name: string;
  description?: string;
}

export interface NewPackage {
  name: string;
  description?: string;
}

export interface Item {
  id: number;
  name: string;
  sku: string;
}

export interface NewItem {
  name: string;
  sku: string;
}

export interface Recipe {
  id: number;
  package_id: number;
  item_id: number;
  quantity: number;
  unit: string;
  valid_from: string; // ISO date string
  valid_to?: string; // ISO date string
}

export interface NewRecipe {
  package_id: number;
  item_id: number;
  quantity: number;
  unit: string;
  valid_from: string; // ISO date string
  valid_to?: string; // ISO date string
}

export interface Batch {
  id: number;
  item_id: number;
  batch_number: string;
  in_date: string; // ISO date string
  expiry_date?: string; // ISO date string
}

export interface NewBatch {
  item_id: number;
  batch_number: string;
  in_date: string; // ISO date string
  expiry_date?: string; // ISO date string
}

export interface PurchaseOrder {
  order_number: string;
  order_date: string; // ISO datetime string
  supplier: string;
}

export interface NewPurchaseOrder {
  order_number: string;
  order_date: string; // ISO datetime string
  supplier: string;
}

export interface ShipmentOrder {
  order_number: string;
  order_date: string; // ISO datetime string
  customer: string;
}

export interface NewShipmentOrder {
  order_number: string;
  order_date: string; // ISO datetime string
  customer: string;
}

export interface InventoryTransaction {
  id: number;
  batch_id: number;
  change_quantity: number;
  change_time: string; // ISO datetime string
  order_number: string;
  order_type: string;
}

export interface InventoryAlert {
  id: number;
  item_id: number;
  threshold: number;
  is_active: boolean;
  triggered_at?: string; // ISO datetime string
}

export interface NewInventoryAlert {
  item_id: number;
  threshold: number;
  is_active: boolean;
  triggered_at?: string; // ISO datetime string
}

// 库存相关类型
export interface BatchStock {
  batch_id: number;
  batch_number: string;
  current_quantity: number;
  in_date: string; // ISO date string
  expiry_date?: string; // ISO date string
}

export interface StockLevel {
  item_id: number;
  item_name: string;
  item_sku: string;
  total_quantity: number;
  batches: BatchStock[];
}

// 订单类型枚举
export enum OrderType {
  Purchase = "PURCHASE",
  Shipment = "SHIPMENT"
}

// 低库存预警元组类型
export type LowStockAlert = [Item, InventoryAlert, number];

// API响应类型
export type ApiResult<T> = T | string; // Tauri commands return Result<T, String>

// 表单状态类型
export interface FormState {
  isLoading: boolean;
  error?: string;
}

// 页面路由类型
export interface RouteConfig {
  path: string;
  label: string;
  icon?: string;
}
