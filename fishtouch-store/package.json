{"name": "fishtouch-store", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@chakra-ui/react": "^3.21.1", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "@types/react-router-dom": "^5.3.3", "date-fns": "^4.1.0", "framer-motion": "^12.23.0", "lucide-react": "^0.525.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.6.3"}, "devDependencies": {"@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "typescript": "~5.6.2", "vite": "^6.0.3", "@tauri-apps/cli": "^2"}}