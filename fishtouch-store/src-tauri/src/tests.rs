#[cfg(test)]
mod tests {

    use crate::database::establish_test_connection;
    use crate::services::{
        package_service::PackageService,
        item_service::ItemService,
        recipe_service::RecipeService,
        batch_service::BatchService,
        inventory_service::InventoryService,
    };
    use crate::models::*;
    use chrono::{NaiveDate, NaiveDateTime};
    use diesel::prelude::*;

    fn setup_test_data(conn: &mut SqliteConnection) -> (Package, Item, Recipe, Batch) {
        // Create a test package
        let new_package = NewPackage {
            name: "Test Package".to_string(),
            description: Some("A test package for unit testing".to_string()),
        };
        let package = PackageService::create(conn, new_package).expect("Failed to create package");

        // Create a test item
        let new_item = NewItem {
            name: "Test Item".to_string(),
            sku: "TEST-001".to_string(),
        };
        let item = ItemService::create(conn, new_item).expect("Failed to create item");

        // Create a test recipe
        let new_recipe = NewRecipe {
            package_id: package.id,
            item_id: item.id,
            quantity: 10.0,
            unit: "pieces".to_string(),
            valid_from: NaiveDate::from_ymd_opt(2024, 1, 1).unwrap(),
            valid_to: None,
        };
        let recipe = RecipeService::create(conn, new_recipe).expect("Failed to create recipe");

        // Create a test batch
        let new_batch = NewBatch {
            item_id: item.id,
            batch_number: "BATCH-001".to_string(),
            in_date: NaiveDate::from_ymd_opt(2024, 1, 15).unwrap(),
            expiry_date: Some(NaiveDate::from_ymd_opt(2024, 12, 31).unwrap()),
        };
        let batch = BatchService::create(conn, new_batch).expect("Failed to create batch");

        (package, item, recipe, batch)
    }

    #[test]
    fn test_package_crud_operations() {
        let mut conn = establish_test_connection();

        // Test create
        let new_package = NewPackage {
            name: "Test Package".to_string(),
            description: Some("A test package".to_string()),
        };
        let package = PackageService::create(&mut conn, new_package).expect("Failed to create package");
        assert_eq!(package.name, "Test Package");
        assert_eq!(package.description, Some("A test package".to_string()));

        // Test get by id
        let retrieved_package = PackageService::get_by_id(&mut conn, package.id).expect("Failed to get package");
        assert_eq!(retrieved_package.id, package.id);
        assert_eq!(retrieved_package.name, package.name);

        // Test get all
        let packages = PackageService::get_all(&mut conn).expect("Failed to get all packages");
        assert!(packages.len() >= 1);

        // Test delete
        let deleted_count = PackageService::delete(&mut conn, package.id).expect("Failed to delete package");
        assert_eq!(deleted_count, 1);

        // Verify deletion
        let result = PackageService::get_by_id(&mut conn, package.id);
        assert!(result.is_err());
    }

    #[test]
    fn test_item_crud_operations() {
        let mut conn = establish_test_connection();
        let (package, _, _, _) = setup_test_data(&mut conn);

        // Test create
        let new_item = NewItem {
            name: "Test Item 2".to_string(),
            sku: "TEST-002".to_string(),
        };
        let item = ItemService::create(&mut conn, new_item).expect("Failed to create item");
        assert_eq!(item.name, "Test Item 2");
        assert_eq!(item.sku, "TEST-002");

        // Test get by id
        let retrieved_item = ItemService::get_by_id(&mut conn, item.id).expect("Failed to get item");
        assert_eq!(retrieved_item.id, item.id);

        // Test get all
        let items = ItemService::get_all(&mut conn).expect("Failed to get all items");
        assert!(items.len() >= 1);

        // Test delete
        let deleted_count = ItemService::delete(&mut conn, item.id).expect("Failed to delete item");
        assert_eq!(deleted_count, 1);
    }

    #[test]
    fn test_recipe_operations() {
        let mut conn = establish_test_connection();
        let (package, item, _, _) = setup_test_data(&mut conn);

        // Test create recipe
        let new_recipe = NewRecipe {
            package_id: package.id,
            item_id: item.id,
            quantity: 5.0,
            unit: "kg".to_string(),
            valid_from: NaiveDate::from_ymd_opt(2024, 2, 1).unwrap(),
            valid_to: Some(NaiveDate::from_ymd_opt(2024, 12, 31).unwrap()),
        };
        let recipe = RecipeService::create(&mut conn, new_recipe).expect("Failed to create recipe");
        assert_eq!(recipe.quantity, 5.0);
        assert_eq!(recipe.unit, "kg");

        // Test get active recipes
        let active_recipes = RecipeService::get_active_recipes_for_package(
            &mut conn, 
            package.id, 
            NaiveDate::from_ymd_opt(2024, 6, 1).unwrap()
        ).expect("Failed to get active recipes");
        assert!(active_recipes.len() >= 1);

        // Test expire recipe
        let expired_recipe = RecipeService::expire_recipe(
            &mut conn, 
            recipe.id, 
            NaiveDate::from_ymd_opt(2024, 3, 1).unwrap()
        ).expect("Failed to expire recipe");
        assert_eq!(expired_recipe.valid_to, Some(NaiveDate::from_ymd_opt(2024, 3, 1).unwrap()));
    }

    #[test]
    fn test_batch_operations() {
        let mut conn = establish_test_connection();
        let (_, item, _, _) = setup_test_data(&mut conn);

        // Test create batch
        let new_batch = NewBatch {
            item_id: item.id,
            batch_number: "BATCH-TEST-002".to_string(),
            in_date: NaiveDate::from_ymd_opt(2024, 2, 1).unwrap(),
            expiry_date: Some(NaiveDate::from_ymd_opt(2024, 8, 1).unwrap()),
        };
        let batch = BatchService::create(&mut conn, new_batch).expect("Failed to create batch");
        assert_eq!(batch.batch_number, "BATCH-TEST-002");

        // Test get by batch number
        let retrieved_batch = BatchService::get_by_batch_number(&mut conn, item.id, "BATCH-TEST-002")
            .expect("Failed to get batch by number");
        assert_eq!(retrieved_batch.id, batch.id);

        // Test get expiring batches
        let expiring_batches = BatchService::get_expiring_batches(
            &mut conn, 
            NaiveDate::from_ymd_opt(2024, 9, 1).unwrap()
        ).expect("Failed to get expiring batches");
        assert!(expiring_batches.len() >= 1);
    }

    #[test]
    fn test_inventory_transactions() {
        let mut conn = establish_test_connection();
        let (_, _, _, batch) = setup_test_data(&mut conn);

        // Test purchase transaction
        let purchase_transaction = InventoryService::record_purchase_transaction(
            &mut conn,
            batch.id,
            100,
            "PO-001".to_string(),
            NaiveDate::from_ymd_opt(2022, 1, 1).unwrap().and_hms_opt(0, 0, 0).unwrap(),
        ).expect("Failed to record purchase transaction");
        
        assert_eq!(purchase_transaction.batch_id, batch.id);
        assert_eq!(purchase_transaction.change_quantity, 100);
        assert_eq!(purchase_transaction.order_type, "PURCHASE");

        // Test shipment transaction
        let shipment_transaction = InventoryService::record_shipment_transaction(
            &mut conn,
            batch.id,
            30,
            "SO-001".to_string(),
            NaiveDate::from_ymd_opt(2022, 1, 2).unwrap().and_hms_opt(0, 0, 0).unwrap(),
        ).expect("Failed to record shipment transaction");
        
        assert_eq!(shipment_transaction.batch_id, batch.id);
        assert_eq!(shipment_transaction.change_quantity, -30);
        assert_eq!(shipment_transaction.order_type, "SHIPMENT");
    }

    #[test]
    fn test_stock_level_calculations() {
        let mut conn = establish_test_connection();
        let (_, item, _, batch) = setup_test_data(&mut conn);

        // Record some transactions
        InventoryService::record_purchase_transaction(
            &mut conn,
            batch.id,
            200,
            "PO-002".to_string(),
            NaiveDate::from_ymd_opt(2022, 1, 1).unwrap().and_hms_opt(0, 0, 0).unwrap(),
        ).expect("Failed to record purchase");

        InventoryService::record_shipment_transaction(
            &mut conn,
            batch.id,
            50,
            "SO-002".to_string(),
            NaiveDate::from_ymd_opt(2022, 1, 2).unwrap().and_hms_opt(0, 0, 0).unwrap(),
        ).expect("Failed to record shipment");

        // Test stock level calculation
        let stock_level = InventoryService::get_current_stock_for_item(&mut conn, item.id)
            .expect("Failed to get stock level");
        
        assert_eq!(stock_level.item_id, item.id);
        assert_eq!(stock_level.total_quantity, 150); // 200 - 50
        assert_eq!(stock_level.batches.len(), 1);
        assert_eq!(stock_level.batches[0].current_quantity, 150);
    }

    #[test]
    fn test_purchase_and_shipment_orders() {
        let mut conn = establish_test_connection();

        // Test purchase order
        let new_purchase_order = NewPurchaseOrder {
            order_number: "PO-TEST-001".to_string(),
            order_date: NaiveDate::from_ymd_opt(2022, 1, 1).unwrap().and_hms_opt(0, 0, 0).unwrap(),
            supplier: "Test Supplier".to_string(),
        };
        let purchase_order = InventoryService::create_purchase_order(&mut conn, new_purchase_order)
            .expect("Failed to create purchase order");
        assert_eq!(purchase_order.order_number, "PO-TEST-001");
        assert_eq!(purchase_order.supplier, "Test Supplier");

        // Test shipment order
        let new_shipment_order = NewShipmentOrder {
            order_number: "SO-TEST-001".to_string(),
            order_date: NaiveDate::from_ymd_opt(2022, 1, 2).unwrap().and_hms_opt(0, 0, 0).unwrap(),
            customer: "Test Customer".to_string(),
        };
        let shipment_order = InventoryService::create_shipment_order(&mut conn, new_shipment_order)
            .expect("Failed to create shipment order");
        assert_eq!(shipment_order.order_number, "SO-TEST-001");
        assert_eq!(shipment_order.customer, "Test Customer");
    }

    #[test]
    fn test_low_stock_alerts() {
        let mut conn = establish_test_connection();
        let (_, item, _, _) = setup_test_data(&mut conn);

        // Create an inventory alert
        let new_alert = NewInventoryAlert {
            item_id: item.id,
            threshold: 50,
            is_active: true,
            triggered_at: None,
        };
        let alert = InventoryService::create_inventory_alert(&mut conn, new_alert)
            .expect("Failed to create inventory alert");
        assert_eq!(alert.item_id, item.id);
        assert_eq!(alert.threshold, 50);

        // Test checking low stock alerts
        let triggered_alerts = InventoryService::check_low_stock_alerts(&mut conn)
            .expect("Failed to check low stock alerts");
        
        // Should trigger since we have no stock transactions yet (0 < 50)
        assert!(triggered_alerts.len() >= 1);
        let (alert_item, alert_info, current_stock) = &triggered_alerts[0];
        assert_eq!(alert_item.id, item.id);
        assert_eq!(alert_info.threshold, 50);
        assert_eq!(*current_stock, 0);
    }
}
