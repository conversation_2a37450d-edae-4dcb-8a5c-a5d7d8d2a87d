// @generated automatically by Diesel CLI.

diesel::table! {
    batches (id) {
        id -> Integer,
        item_id -> Integer,
        batch_number -> Text,
        in_date -> Date,
        expiry_date -> Nullable<Date>,
    }
}

diesel::table! {
    inventory_alerts (id) {
        id -> Integer,
        item_id -> Integer,
        threshold -> Integer,
        is_active -> Bo<PERSON>,
        triggered_at -> Nullable<Timestamp>,
    }
}

diesel::table! {
    inventory_transactions (id) {
        id -> Integer,
        batch_id -> Integer,
        change_quantity -> Integer,
        change_time -> Timestamp,
        order_number -> Text,
        order_type -> Text,
    }
}

diesel::table! {
    items (id) {
        id -> Integer,
        name -> Text,
        sku -> Text,
    }
}

diesel::table! {
    packages (id) {
        id -> Integer,
        name -> Text,
        description -> Nullable<Text>,
    }
}

diesel::table! {
    purchase_orders (order_number) {
        order_number -> Text,
        order_date -> Timestamp,
        supplier -> Text,
    }
}

diesel::table! {
    recipes (id) {
        id -> Integer,
        package_id -> Integer,
        item_id -> Integer,
        quantity -> Float,
        unit -> Text,
        valid_from -> Date,
        valid_to -> Nullable<Date>,
    }
}

diesel::table! {
    shipment_orders (order_number) {
        order_number -> Text,
        order_date -> Timestamp,
        customer -> Text,
    }
}

diesel::joinable!(batches -> items (item_id));
diesel::joinable!(inventory_alerts -> items (item_id));
diesel::joinable!(inventory_transactions -> batches (batch_id));
diesel::joinable!(recipes -> items (item_id));
diesel::joinable!(recipes -> packages (package_id));

diesel::allow_tables_to_appear_in_same_query!(
    batches,
    inventory_alerts,
    inventory_transactions,
    items,
    packages,
    purchase_orders,
    recipes,
    shipment_orders,
);
