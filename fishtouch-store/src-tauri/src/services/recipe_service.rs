use diesel::prelude::*;
use chrono::NaiveDate;
use crate::database::DbConnection;
use crate::models::{Recipe, NewRecipe, Package, Item};
use crate::schema::{recipes, packages, items};

pub struct RecipeService;

impl RecipeService {
    pub fn create(conn: &mut DbConnection, new_recipe: NewRecipe) -> QueryResult<Recipe> {
        diesel::insert_into(recipes::table)
            .values(&new_recipe)
            .execute(conn)?;

        // Get the last inserted row
        recipes::table
            .order(recipes::id.desc())
            .select(Recipe::as_select())
            .first(conn)
    }

    pub fn get_all(conn: &mut DbConnection) -> QueryResult<Vec<Recipe>> {
        recipes::table.select(Recipe::as_select()).load(conn)
    }

    pub fn get_by_id(conn: &mut DbConnection, recipe_id: i32) -> QueryResult<Recipe> {
        recipes::table
            .filter(recipes::id.eq(recipe_id))
            .select(Recipe::as_select())
            .first(conn)
    }

    pub fn get_by_package_id(conn: &mut DbConnection, package_id: i32) -> QueryResult<Vec<Recipe>> {
        recipes::table
            .filter(recipes::package_id.eq(package_id))
            .select(Recipe::as_select())
            .load(conn)
    }

    pub fn get_active_recipes_for_package(
        conn: &mut DbConnection,
        package_id: i32,
        date: NaiveDate,
    ) -> QueryResult<Vec<Recipe>> {
        recipes::table
            .filter(recipes::package_id.eq(package_id))
            .filter(recipes::valid_from.le(date))
            .filter(recipes::valid_to.is_null().or(recipes::valid_to.ge(date)))
            .select(Recipe::as_select())
            .load(conn)
    }

    pub fn get_recipes_with_details(
        conn: &mut DbConnection,
        package_id: i32,
    ) -> QueryResult<Vec<(Recipe, Package, Item)>> {
        recipes::table
            .inner_join(packages::table)
            .inner_join(items::table)
            .filter(recipes::package_id.eq(package_id))
            .select((Recipe::as_select(), Package::as_select(), Item::as_select()))
            .load(conn)
    }

    pub fn update(
        conn: &mut DbConnection,
        recipe_id: i32,
        quantity: Option<f32>,
        unit: Option<String>,
        valid_from: Option<NaiveDate>,
        valid_to: Option<Option<NaiveDate>>,
    ) -> QueryResult<Recipe> {
        let target = recipes::table.filter(recipes::id.eq(recipe_id));

        // Build the update dynamically based on provided fields
        if let Some(quantity) = quantity {
            diesel::update(target)
                .set(recipes::quantity.eq(quantity))
                .execute(conn)?;
        }
        if let Some(unit) = unit {
            diesel::update(target)
                .set(recipes::unit.eq(unit))
                .execute(conn)?;
        }
        if let Some(valid_from) = valid_from {
            diesel::update(target)
                .set(recipes::valid_from.eq(valid_from))
                .execute(conn)?;
        }
        if let Some(valid_to) = valid_to {
            diesel::update(target)
                .set(recipes::valid_to.eq(valid_to))
                .execute(conn)?;
        }

        // Return the updated recipe
        Self::get_by_id(conn, recipe_id)
    }

    pub fn delete(conn: &mut DbConnection, recipe_id: i32) -> QueryResult<usize> {
        diesel::delete(recipes::table.filter(recipes::id.eq(recipe_id))).execute(conn)
    }

    pub fn expire_recipe(
        conn: &mut DbConnection,
        recipe_id: i32,
        expiry_date: NaiveDate,
    ) -> QueryResult<Recipe> {
        diesel::update(recipes::table.filter(recipes::id.eq(recipe_id)))
            .set(recipes::valid_to.eq(Some(expiry_date)))
            .execute(conn)?;

        Self::get_by_id(conn, recipe_id)
    }
}
