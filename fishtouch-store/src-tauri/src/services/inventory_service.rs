use diesel::prelude::*;
use chrono::NaiveDateTime;
use serde::Serialize;
use crate::database::DbConnection;
use crate::models::{
    InventoryTransaction, NewInventoryTransaction, 
    PurchaseOrder, NewPurchaseOrder,
    ShipmentOrder, NewShipmentOrder,
    InventoryAlert, NewInventoryAlert,
    Batch, Item, OrderType
};
use crate::schema::{
    inventory_transactions, purchase_orders, shipment_orders, 
    inventory_alerts, batches, items
};

pub struct InventoryService;

#[derive(Debug, Serialize)]
pub struct StockLevel {
    pub item_id: i32,
    pub item_name: String,
    pub item_sku: String,
    pub total_quantity: i32,
    pub batches: Vec<BatchStock>,
}

#[derive(Debug, Serialize)]
pub struct BatchStock {
    pub batch_id: i32,
    pub batch_number: String,
    pub current_quantity: i32,
    pub in_date: chrono::NaiveDate,
    pub expiry_date: Option<chrono::NaiveDate>,
}

impl InventoryService {
    // Purchase Order Management
    pub fn create_purchase_order(
        conn: &mut DbConnection,
        new_order: NewPurchaseOrder,
    ) -> QueryResult<PurchaseOrder> {
        diesel::insert_into(purchase_orders::table)
            .values(&new_order)
            .execute(conn)?;

        // Get the last inserted row
        purchase_orders::table
            .order(purchase_orders::order_date.desc())
            .select(PurchaseOrder::as_select())
            .first(conn)
    }

    pub fn create_shipment_order(
        conn: &mut DbConnection,
        new_order: NewShipmentOrder,
    ) -> QueryResult<ShipmentOrder> {
        diesel::insert_into(shipment_orders::table)
            .values(&new_order)
            .execute(conn)?;

        // Get the last inserted row
        shipment_orders::table
            .order(shipment_orders::order_date.desc())
            .select(ShipmentOrder::as_select())
            .first(conn)
    }

    // Inventory Transaction Management
    pub fn record_purchase_transaction(
        conn: &mut DbConnection,
        batch_id: i32,
        quantity: i32,
        order_number: String,
        timestamp: NaiveDateTime,
    ) -> QueryResult<InventoryTransaction> {
        let new_transaction = NewInventoryTransaction {
            batch_id,
            change_quantity: quantity, // Positive for purchases
            change_time: timestamp,
            order_number,
            order_type: OrderType::Purchase.to_string(),
        };

        diesel::insert_into(inventory_transactions::table)
            .values(&new_transaction)
            .execute(conn)?;

        // Get the last inserted row
        inventory_transactions::table
            .order(inventory_transactions::id.desc())
            .select(InventoryTransaction::as_select())
            .first(conn)
    }

    pub fn record_shipment_transaction(
        conn: &mut DbConnection,
        batch_id: i32,
        quantity: i32,
        order_number: String,
        timestamp: NaiveDateTime,
    ) -> QueryResult<InventoryTransaction> {
        let new_transaction = NewInventoryTransaction {
            batch_id,
            change_quantity: -quantity, // Negative for shipments
            change_time: timestamp,
            order_number,
            order_type: OrderType::Shipment.to_string(),
        };

        diesel::insert_into(inventory_transactions::table)
            .values(&new_transaction)
            .execute(conn)?;

        // Get the last inserted row
        inventory_transactions::table
            .order(inventory_transactions::id.desc())
            .select(InventoryTransaction::as_select())
            .first(conn)
    }

    // Stock Level Calculations
    pub fn get_current_stock_for_item(
        conn: &mut DbConnection,
        item_id: i32,
    ) -> QueryResult<StockLevel> {
        // Get item details
        let item: Item = items::table
            .filter(items::id.eq(item_id))
            .select(Item::as_select())
            .first(conn)?;

        // Get all batches for this item
        let batches: Vec<Batch> = batches::table
            .filter(batches::item_id.eq(item_id))
            .select(Batch::as_select())
            .load(conn)?;

        let mut batch_stocks = Vec::new();
        let mut total_quantity = 0;

        for batch in batches {
            let quantity = Self::get_batch_current_quantity(conn, batch.id)?;
            total_quantity += quantity;
            
            batch_stocks.push(BatchStock {
                batch_id: batch.id,
                batch_number: batch.batch_number,
                current_quantity: quantity,
                in_date: batch.in_date,
                expiry_date: batch.expiry_date,
            });
        }

        Ok(StockLevel {
            item_id: item.id,
            item_name: item.name,
            item_sku: item.sku,
            total_quantity,
            batches: batch_stocks,
        })
    }

    pub fn get_batch_current_quantity(
        conn: &mut DbConnection,
        batch_id: i32,
    ) -> QueryResult<i32> {
        use diesel::dsl::*;

        let total: Option<i64> = inventory_transactions::table
            .filter(inventory_transactions::batch_id.eq(batch_id))
            .select(sum(inventory_transactions::change_quantity))
            .first(conn)?;

        Ok(total.unwrap_or(0) as i32)
    }

    pub fn get_all_stock_levels(conn: &mut DbConnection) -> QueryResult<Vec<StockLevel>> {
        let items: Vec<Item> = items::table.select(Item::as_select()).load(conn)?;
        let mut stock_levels = Vec::new();

        for item in items {
            let stock_level = Self::get_current_stock_for_item(conn, item.id)?;
            stock_levels.push(stock_level);
        }

        Ok(stock_levels)
    }

    // Inventory Alerts Management
    pub fn create_inventory_alert(
        conn: &mut DbConnection,
        new_alert: NewInventoryAlert,
    ) -> QueryResult<InventoryAlert> {
        diesel::insert_into(inventory_alerts::table)
            .values(&new_alert)
            .execute(conn)?;

        // Get the last inserted row
        inventory_alerts::table
            .order(inventory_alerts::id.desc())
            .select(InventoryAlert::as_select())
            .first(conn)
    }

    pub fn check_low_stock_alerts(conn: &mut DbConnection) -> QueryResult<Vec<(Item, InventoryAlert, i32)>> {
        let alerts: Vec<(InventoryAlert, Item)> = inventory_alerts::table
            .inner_join(items::table)
            .filter(inventory_alerts::is_active.eq(true))
            .select((InventoryAlert::as_select(), Item::as_select()))
            .load(conn)?;

        let mut triggered_alerts = Vec::new();

        for (alert, item) in alerts {
            let current_stock = Self::get_current_stock_for_item(conn, item.id)?;
            if current_stock.total_quantity <= alert.threshold {
                triggered_alerts.push((item, alert, current_stock.total_quantity));
            }
        }

        Ok(triggered_alerts)
    }

    pub fn get_transaction_history(
        conn: &mut DbConnection,
        batch_id: Option<i32>,
        order_number: Option<String>,
    ) -> QueryResult<Vec<(InventoryTransaction, Batch, Item)>> {
        let mut query = inventory_transactions::table
            .inner_join(batches::table.inner_join(items::table))
            .into_boxed();

        if let Some(batch_id) = batch_id {
            query = query.filter(inventory_transactions::batch_id.eq(batch_id));
        }

        if let Some(order_number) = order_number {
            query = query.filter(inventory_transactions::order_number.eq(order_number));
        }

        query
            .select((InventoryTransaction::as_select(), Batch::as_select(), Item::as_select()))
            .load(conn)
    }
}
