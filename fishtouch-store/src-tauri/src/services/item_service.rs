use diesel::prelude::*;
use crate::database::DbConnection;
use crate::models::{Item, NewItem};
use crate::schema::items;

pub struct ItemService;

impl ItemService {
    pub fn create(conn: &mut DbConnection, new_item: NewItem) -> QueryResult<Item> {
        diesel::insert_into(items::table)
            .values(&new_item)
            .execute(conn)?;

        // Get the last inserted row
        items::table
            .order(items::id.desc())
            .select(Item::as_select())
            .first(conn)
    }

    pub fn get_all(conn: &mut DbConnection) -> QueryResult<Vec<Item>> {
        items::table.select(Item::as_select()).load(conn)
    }

    pub fn get_by_id(conn: &mut DbConnection, item_id: i32) -> QueryResult<Item> {
        items::table
            .filter(items::id.eq(item_id))
            .select(Item::as_select())
            .first(conn)
    }

    pub fn get_by_sku(conn: &mut DbConnection, sku: &str) -> QueryResult<Item> {
        items::table
            .filter(items::sku.eq(sku))
            .select(Item::as_select())
            .first(conn)
    }

    pub fn update(
        conn: &mut DbConnection,
        item_id: i32,
        name: Option<String>,
        sku: Option<String>,
    ) -> QueryResult<Item> {
        let target = items::table.filter(items::id.eq(item_id));

        match (name, sku) {
            (Some(name), Some(sku)) => {
                diesel::update(target)
                    .set((items::name.eq(name), items::sku.eq(sku)))
                    .execute(conn)?;
            }
            (Some(name), None) => {
                diesel::update(target)
                    .set(items::name.eq(name))
                    .execute(conn)?;
            }
            (None, Some(sku)) => {
                diesel::update(target)
                    .set(items::sku.eq(sku))
                    .execute(conn)?;
            }
            (None, None) => {
                // No fields to update
            }
        }

        // Return the updated item
        Self::get_by_id(conn, item_id)
    }

    pub fn delete(conn: &mut DbConnection, item_id: i32) -> QueryResult<usize> {
        diesel::delete(items::table.filter(items::id.eq(item_id))).execute(conn)
    }

    pub fn search_by_name(conn: &mut DbConnection, search_term: &str) -> QueryResult<Vec<Item>> {
        items::table
            .filter(items::name.like(format!("%{}%", search_term)))
            .select(Item::as_select())
            .load(conn)
    }

    pub fn search_by_sku(conn: &mut DbConnection, search_term: &str) -> QueryResult<Vec<Item>> {
        items::table
            .filter(items::sku.like(format!("%{}%", search_term)))
            .select(Item::as_select())
            .load(conn)
    }
}
