use diesel::prelude::*;
use chrono::NaiveDate;
use crate::database::DbConnection;
use crate::models::{<PERSON><PERSON>, NewBatch, Item};
use crate::schema::{batches, items};

pub struct BatchService;

impl BatchService {
    pub fn create(conn: &mut DbConnection, new_batch: NewBatch) -> QueryResult<Batch> {
        diesel::insert_into(batches::table)
            .values(&new_batch)
            .execute(conn)?;

        // Get the last inserted row
        batches::table
            .order(batches::id.desc())
            .select(Batch::as_select())
            .first(conn)
    }

    pub fn get_all(conn: &mut DbConnection) -> QueryResult<Vec<Batch>> {
        batches::table.select(Batch::as_select()).load(conn)
    }

    pub fn get_by_id(conn: &mut DbConnection, batch_id: i32) -> QueryResult<Batch> {
        batches::table
            .filter(batches::id.eq(batch_id))
            .select(Batch::as_select())
            .first(conn)
    }

    pub fn get_by_item_id(conn: &mut DbConnection, item_id: i32) -> QueryResult<Vec<Batch>> {
        batches::table
            .filter(batches::item_id.eq(item_id))
            .select(Batch::as_select())
            .load(conn)
    }

    pub fn get_by_batch_number(
        conn: &mut DbConnection,
        item_id: i32,
        batch_number: &str,
    ) -> QueryResult<Batch> {
        batches::table
            .filter(batches::item_id.eq(item_id))
            .filter(batches::batch_number.eq(batch_number))
            .select(Batch::as_select())
            .first(conn)
    }

    pub fn get_expiring_batches(
        conn: &mut DbConnection,
        before_date: NaiveDate,
    ) -> QueryResult<Vec<(Batch, Item)>> {
        batches::table
            .inner_join(items::table)
            .filter(batches::expiry_date.is_not_null())
            .filter(batches::expiry_date.le(before_date))
            .select((Batch::as_select(), Item::as_select()))
            .load(conn)
    }

    pub fn get_batches_with_items(conn: &mut DbConnection) -> QueryResult<Vec<(Batch, Item)>> {
        batches::table
            .inner_join(items::table)
            .select((Batch::as_select(), Item::as_select()))
            .load(conn)
    }

    pub fn update(
        conn: &mut DbConnection,
        batch_id: i32,
        batch_number: Option<String>,
        in_date: Option<NaiveDate>,
        expiry_date: Option<Option<NaiveDate>>,
    ) -> QueryResult<Batch> {
        let target = batches::table.filter(batches::id.eq(batch_id));

        // Build the update dynamically based on provided fields
        if let Some(batch_number) = batch_number {
            diesel::update(target)
                .set(batches::batch_number.eq(batch_number))
                .execute(conn)?;
        }
        if let Some(in_date) = in_date {
            diesel::update(target)
                .set(batches::in_date.eq(in_date))
                .execute(conn)?;
        }
        if let Some(expiry_date) = expiry_date {
            diesel::update(target)
                .set(batches::expiry_date.eq(expiry_date))
                .execute(conn)?;
        }

        // Return the updated batch
        Self::get_by_id(conn, batch_id)
    }

    pub fn delete(conn: &mut DbConnection, batch_id: i32) -> QueryResult<usize> {
        diesel::delete(batches::table.filter(batches::id.eq(batch_id))).execute(conn)
    }

    pub fn search_by_batch_number(
        conn: &mut DbConnection,
        search_term: &str,
    ) -> QueryResult<Vec<Batch>> {
        batches::table
            .filter(batches::batch_number.like(format!("%{}%", search_term)))
            .select(Batch::as_select())
            .load(conn)
    }
}
