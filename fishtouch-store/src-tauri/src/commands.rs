use tauri::State;
use std::sync::{Arc, Mutex};
use chrono::{NaiveDate, NaiveDateTime};

use crate::database::DbConnection;
use crate::models::*;
use crate::services::*;

pub type AppState = Arc<Mutex<DbConnection>>;

// Package Commands
#[tauri::command]
pub fn create_package(
    state: State<AppState>,
    name: String,
    description: Option<String>,
) -> Result<Package, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;
    let new_package = NewPackage { name, description };
    
    PackageService::create(&mut *conn, new_package)
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub fn get_all_packages(state: State<AppState>) -> Result<Vec<Package>, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;
    PackageService::get_all(&mut *conn).map_err(|e| e.to_string())
}

#[tauri::command]
pub fn get_package_by_id(state: State<AppState>, id: i32) -> Result<Package, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;
    PackageService::get_by_id(&mut *conn, id).map_err(|e| e.to_string())
}

#[tauri::command]
pub fn delete_package(state: State<AppState>, id: i32) -> Result<usize, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;
    PackageService::delete(&mut *conn, id).map_err(|e| e.to_string())
}

// Item Commands
#[tauri::command]
pub fn create_item(
    state: State<AppState>,
    name: String,
    sku: String,
) -> Result<Item, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;
    let new_item = NewItem { name, sku };
    
    ItemService::create(&mut *conn, new_item)
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub fn get_all_items(state: State<AppState>) -> Result<Vec<Item>, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;
    ItemService::get_all(&mut *conn).map_err(|e| e.to_string())
}

#[tauri::command]
pub fn get_item_by_id(state: State<AppState>, id: i32) -> Result<Item, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;
    ItemService::get_by_id(&mut *conn, id).map_err(|e| e.to_string())
}

#[tauri::command]
pub fn get_item_by_sku(state: State<AppState>, sku: String) -> Result<Item, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;
    ItemService::get_by_sku(&mut *conn, &sku).map_err(|e| e.to_string())
}

#[tauri::command]
pub fn delete_item(state: State<AppState>, id: i32) -> Result<usize, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;
    ItemService::delete(&mut *conn, id).map_err(|e| e.to_string())
}

// Recipe Commands
#[tauri::command]
pub fn create_recipe(
    state: State<AppState>,
    package_id: i32,
    item_id: i32,
    quantity: f32,
    unit: String,
    valid_from: String, // ISO date string
    valid_to: Option<String>, // ISO date string
) -> Result<Recipe, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;
    
    let valid_from = NaiveDate::parse_from_str(&valid_from, "%Y-%m-%d")
        .map_err(|e| format!("Invalid valid_from date: {}", e))?;
    
    let valid_to = if let Some(date_str) = valid_to {
        Some(NaiveDate::parse_from_str(&date_str, "%Y-%m-%d")
            .map_err(|e| format!("Invalid valid_to date: {}", e))?)
    } else {
        None
    };
    
    let new_recipe = NewRecipe {
        package_id,
        item_id,
        quantity,
        unit,
        valid_from,
        valid_to,
    };
    
    RecipeService::create(&mut *conn, new_recipe)
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub fn get_recipes_by_package_id(state: State<AppState>, package_id: i32) -> Result<Vec<Recipe>, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;
    RecipeService::get_by_package_id(&mut *conn, package_id).map_err(|e| e.to_string())
}

// Batch Commands
#[tauri::command]
pub fn create_batch(
    state: State<AppState>,
    item_id: i32,
    batch_number: String,
    in_date: String, // ISO date string
    expiry_date: Option<String>, // ISO date string
) -> Result<Batch, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;
    
    let in_date = NaiveDate::parse_from_str(&in_date, "%Y-%m-%d")
        .map_err(|e| format!("Invalid in_date: {}", e))?;
    
    let expiry_date = if let Some(date_str) = expiry_date {
        Some(NaiveDate::parse_from_str(&date_str, "%Y-%m-%d")
            .map_err(|e| format!("Invalid expiry_date: {}", e))?)
    } else {
        None
    };
    
    let new_batch = NewBatch {
        item_id,
        batch_number,
        in_date,
        expiry_date,
    };
    
    BatchService::create(&mut *conn, new_batch)
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub fn get_batches_by_item_id(state: State<AppState>, item_id: i32) -> Result<Vec<Batch>, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;
    BatchService::get_by_item_id(&mut *conn, item_id).map_err(|e| e.to_string())
}

// Inventory Commands
#[tauri::command]
pub fn create_purchase_order(
    state: State<AppState>,
    order_number: String,
    order_date: String, // ISO datetime string
    supplier: String,
) -> Result<PurchaseOrder, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;

    let order_date = NaiveDateTime::parse_from_str(&order_date, "%Y-%m-%dT%H:%M:%S")
        .map_err(|e| format!("Invalid order_date: {}", e))?;

    let new_order = NewPurchaseOrder {
        order_number,
        order_date,
        supplier,
    };

    InventoryService::create_purchase_order(&mut *conn, new_order)
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub fn create_shipment_order(
    state: State<AppState>,
    order_number: String,
    order_date: String, // ISO datetime string
    customer: String,
) -> Result<ShipmentOrder, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;

    let order_date = NaiveDateTime::parse_from_str(&order_date, "%Y-%m-%dT%H:%M:%S")
        .map_err(|e| format!("Invalid order_date: {}", e))?;

    let new_order = NewShipmentOrder {
        order_number,
        order_date,
        customer,
    };

    InventoryService::create_shipment_order(&mut *conn, new_order)
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub fn record_purchase_transaction(
    state: State<AppState>,
    batch_id: i32,
    quantity: i32,
    order_number: String,
    timestamp: String, // ISO datetime string
) -> Result<InventoryTransaction, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;

    let timestamp = NaiveDateTime::parse_from_str(&timestamp, "%Y-%m-%dT%H:%M:%S")
        .map_err(|e| format!("Invalid timestamp: {}", e))?;

    InventoryService::record_purchase_transaction(&mut *conn, batch_id, quantity, order_number, timestamp)
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub fn record_shipment_transaction(
    state: State<AppState>,
    batch_id: i32,
    quantity: i32,
    order_number: String,
    timestamp: String, // ISO datetime string
) -> Result<InventoryTransaction, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;

    let timestamp = NaiveDateTime::parse_from_str(&timestamp, "%Y-%m-%dT%H:%M:%S")
        .map_err(|e| format!("Invalid timestamp: {}", e))?;

    InventoryService::record_shipment_transaction(&mut *conn, batch_id, quantity, order_number, timestamp)
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub fn get_current_stock_for_item(state: State<AppState>, item_id: i32) -> Result<StockLevel, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;
    InventoryService::get_current_stock_for_item(&mut *conn, item_id)
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub fn get_all_stock_levels(state: State<AppState>) -> Result<Vec<StockLevel>, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;
    InventoryService::get_all_stock_levels(&mut *conn)
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub fn check_low_stock_alerts(state: State<AppState>) -> Result<Vec<(Item, InventoryAlert, i32)>, String> {
    let mut conn = state.lock().map_err(|e| e.to_string())?;
    InventoryService::check_low_stock_alerts(&mut *conn)
        .map_err(|e| e.to_string())
}
