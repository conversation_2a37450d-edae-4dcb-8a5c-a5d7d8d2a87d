use diesel::prelude::*;
use serde::{Deserialize, Serialize};
use chrono::{NaiveDate, NaiveDateTime};

use crate::schema::*;

// Package models
#[derive(Queryable, Selectable, Serialize, Deserialize, Debug, Clone)]
#[diesel(table_name = packages)]
pub struct Package {
    pub id: i32,
    pub name: String,
    pub description: Option<String>,
}

#[derive(Insertable, Deserialize)]
#[diesel(table_name = packages)]
pub struct NewPackage {
    pub name: String,
    pub description: Option<String>,
}

// Item models
#[derive(Queryable, Selectable, Serialize, Deserialize, Debug, Clone)]
#[diesel(table_name = items)]
pub struct Item {
    pub id: i32,
    pub name: String,
    pub sku: String,
}

#[derive(Insertable, Deserialize)]
#[diesel(table_name = items)]
pub struct NewItem {
    pub name: String,
    pub sku: String,
}

// Recipe models
#[derive(Queryable, Selectable, Serialize, Deserialize, Debug, Clone)]
#[diesel(table_name = recipes)]
pub struct Recipe {
    pub id: i32,
    pub package_id: i32,
    pub item_id: i32,
    pub quantity: f32,
    pub unit: String,
    pub valid_from: NaiveDate,
    pub valid_to: Option<NaiveDate>,
}

#[derive(Insertable, Deserialize)]
#[diesel(table_name = recipes)]
pub struct NewRecipe {
    pub package_id: i32,
    pub item_id: i32,
    pub quantity: f32,
    pub unit: String,
    pub valid_from: NaiveDate,
    pub valid_to: Option<NaiveDate>,
}

// Batch models
#[derive(Queryable, Selectable, Serialize, Deserialize, Debug, Clone)]
#[diesel(table_name = batches)]
pub struct Batch {
    pub id: i32,
    pub item_id: i32,
    pub batch_number: String,
    pub in_date: NaiveDate,
    pub expiry_date: Option<NaiveDate>,
}

#[derive(Insertable, Deserialize)]
#[diesel(table_name = batches)]
pub struct NewBatch {
    pub item_id: i32,
    pub batch_number: String,
    pub in_date: NaiveDate,
    pub expiry_date: Option<NaiveDate>,
}

// Purchase Order models
#[derive(Queryable, Selectable, Serialize, Deserialize, Debug, Clone)]
#[diesel(table_name = purchase_orders)]
pub struct PurchaseOrder {
    pub order_number: String,
    pub order_date: NaiveDateTime,
    pub supplier: String,
}

#[derive(Insertable, Deserialize)]
#[diesel(table_name = purchase_orders)]
pub struct NewPurchaseOrder {
    pub order_number: String,
    pub order_date: NaiveDateTime,
    pub supplier: String,
}

// Shipment Order models
#[derive(Queryable, Selectable, Serialize, Deserialize, Debug, Clone)]
#[diesel(table_name = shipment_orders)]
pub struct ShipmentOrder {
    pub order_number: String,
    pub order_date: NaiveDateTime,
    pub customer: String,
}

#[derive(Insertable, Deserialize)]
#[diesel(table_name = shipment_orders)]
pub struct NewShipmentOrder {
    pub order_number: String,
    pub order_date: NaiveDateTime,
    pub customer: String,
}

// Inventory Transaction models
#[derive(Queryable, Selectable, Serialize, Deserialize, Debug, Clone)]
#[diesel(table_name = inventory_transactions)]
pub struct InventoryTransaction {
    pub id: i32,
    pub batch_id: i32,
    pub change_quantity: i32,
    pub change_time: NaiveDateTime,
    pub order_number: String,
    pub order_type: String,
}

#[derive(Insertable, Deserialize)]
#[diesel(table_name = inventory_transactions)]
pub struct NewInventoryTransaction {
    pub batch_id: i32,
    pub change_quantity: i32,
    pub change_time: NaiveDateTime,
    pub order_number: String,
    pub order_type: String,
}

// Inventory Alert models
#[derive(Queryable, Selectable, Serialize, Deserialize, Debug, Clone)]
#[diesel(table_name = inventory_alerts)]
pub struct InventoryAlert {
    pub id: i32,
    pub item_id: i32,
    pub threshold: i32,
    pub is_active: bool,
    pub triggered_at: Option<NaiveDateTime>,
}

#[derive(Insertable, Deserialize)]
#[diesel(table_name = inventory_alerts)]
pub struct NewInventoryAlert {
    pub item_id: i32,
    pub threshold: i32,
    pub is_active: bool,
    pub triggered_at: Option<NaiveDateTime>,
}

// Enums for order types
#[derive(Serialize, Deserialize, Debug, Clone)]
pub enum OrderType {
    Purchase,
    Shipment,
}

impl ToString for OrderType {
    fn to_string(&self) -> String {
        match self {
            OrderType::Purchase => "PURCHASE".to_string(),
            OrderType::Shipment => "SHIPMENT".to_string(),
        }
    }
}

impl From<String> for OrderType {
    fn from(s: String) -> Self {
        match s.as_str() {
            "PURCHASE" => OrderType::Purchase,
            "SHIPMENT" => OrderType::Shipment,
            _ => panic!("Invalid order type: {}", s),
        }
    }
}
