Docs:
https://v2.tauri.app/plugin/sql/
DB:
SQLite
```mermaid
erDiagram
    PACKAGE {
        int id PK "套餐ID"
        string name "套餐名称"
        text description "套餐描述"
    }
    ITEM {
        int id PK "单品ID"
        string name "单品名称"
        string sku "库存单位编码"
    }
    RECIPE {
        int id PK "配方ID"
        int package_id FK "套餐ID"
        int item_id FK "单品ID"
        decimal quantity "每份套餐中此单品数量"
        string unit "数量单位"
        date valid_from "生效日期"
        date valid_to "失效日期"
    }
    BATCH {
        int id PK "批次ID"
        int item_id FK "单品ID"
        string batch_number "批次号"
        date in_date "入库日期"
        date expiry_date "失效日期"
    }
    INVENTORY_TRANSACTION {
        int id PK "流水ID"
        int batch_id FK "批次ID"
        int change_quantity "变动数量（正入库/负出库）"
        datetime change_time "变动时间"
        string order_number "关联单号"
        string order_type "单号类型：PURCHASE/SHIPMENT"
    }
    PURCHASE_ORDER {
        string order_number PK "采购单号"
        datetime order_date "采购日期"
        string supplier "供应商"
    }
    SHIPMENT_ORDER {
        string order_number PK "发货单号"
        datetime order_date "发货日期"
        string customer "客户"
    }
    INVENTORY_ALERT {
        int id PK "预警ID"
        int item_id FK "单品ID"
        int threshold "库存阈值"
        boolean is_active "是否启用"
        datetime triggered_at "上次触发时间"
    }

    PACKAGE ||--o{ RECIPE : "定义"
    ITEM    ||--o{ RECIPE : "包含"
    ITEM    ||--o{ BATCH  : "拥有"
    BATCH   ||--o{ INVENTORY_TRANSACTION : "记录"
    PURCHASE_ORDER ||--o{ INVENTORY_TRANSACTION : "入库流水"
    SHIPMENT_ORDER ||--o{ INVENTORY_TRANSACTION : "出库流水"
    ITEM    ||--o{ INVENTORY_ALERT : "设置"

```



